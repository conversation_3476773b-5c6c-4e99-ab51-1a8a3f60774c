import { db } from './db'
import { posts, tags, postTags } from '@/db/schema'
import { eq, desc, and } from 'drizzle-orm'

export interface Post {
  id: number
  title: string
  slug: string
  content: string
  excerpt?: string | null
  published: boolean
  createdAt: Date | null
  updatedAt: Date | null
  tags?: Tag[]
}

export interface Tag {
  id: number
  name: string
  slug: string
}

// 获取所有已发布的文章
export async function getPublishedPosts(): Promise<Post[]> {
  try {
    const result = await db
      .select()
      .from(posts)
      .where(eq(posts.published, true))
      .orderBy(desc(posts.createdAt))
    
    return result
  } catch (error) {
    console.error('Error fetching posts:', error)
    return []
  }
}

// 根据 slug 获取文章
export async function getPostBySlug(slug: string): Promise<Post | null> {
  try {
    const result = await db
      .select()
      .from(posts)
      .where(and(eq(posts.slug, slug), eq(posts.published, true)))
      .limit(1)
    
    return result[0] || null
  } catch (error) {
    console.error('Error fetching post by slug:', error)
    return null
  }
}

// 创建新文章
export async function createPost(postData: {
  title: string
  slug: string
  content: string
  excerpt?: string
  published?: boolean
}): Promise<Post | null> {
  try {
    const result = await db
      .insert(posts)
      .values({
        ...postData,
        published: postData.published ?? false,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('Error creating post:', error)
    return null
  }
}

// 获取所有标签
export async function getAllTags(): Promise<Tag[]> {
  try {
    const result = await db.select().from(tags)
    return result
  } catch (error) {
    console.error('Error fetching tags:', error)
    return []
  }
}