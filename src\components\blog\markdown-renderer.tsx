'use client'

import { useEffect, useState } from 'react'

interface MarkdownRendererProps {
  content: string
}

export default function MarkdownRenderer({ content }: MarkdownRendererProps) {
  const [htmlContent, setHtmlContent] = useState('')

  useEffect(() => {
    // 简单的 Markdown 转换
    const convertMarkdown = (markdown: string) => {
      return markdown
        // 标题
        .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mt-6 mb-3">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mt-8 mb-4">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mt-8 mb-6">$1</h1>')
        // 代码块
        .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-gray-100 p-4 rounded-lg overflow-x-auto my-4"><code class="language-$1">$2</code></pre>')
        // 行内代码
        .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-2 py-1 rounded text-sm">$1</code>')
        // 列表
        .replace(/^\- (.*$)/gim, '<li class="ml-4">• $1</li>')
        .replace(/(<li.*<\/li>)/s, '<ul class="my-4">$1</ul>')
        // 段落
        .replace(/\n\n/g, '</p><p class="mb-4">')
        // 换行
        .replace(/\n/g, '<br>')
    }

    setHtmlContent(`<div class="prose max-w-none">${convertMarkdown(content)}</div>`)
  }, [content])

  return <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
}