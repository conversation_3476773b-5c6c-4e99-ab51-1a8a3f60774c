'use client'

import { useEffect, useRef, useState } from 'react'
import * as THREE from 'three'

interface BackgroundCanvasProps {
  darkMode?: boolean
  mousePosition?: { x: number; y: number }
}

export default function BackgroundCanvas({ darkMode = true, mousePosition }: BackgroundCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sceneRef = useRef<{
    scene: THREE.Scene
    camera: THREE.PerspectiveCamera
    renderer: THREE.WebGLRenderer
    stars: THREE.Points
    nebulae: THREE.Group
    animationId: number | null
  } | null>(null)

  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient || !canvasRef.current) return

    // 初始化Three.js场景
    const scene = new THREE.Scene()
    
    // 设置相机
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      2000
    )
    camera.position.z = 1000

    // 设置渲染器
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: true,
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // 创建5000颗星星粒子系统
    const starsGeometry = new THREE.BufferGeometry()
    const starsCount = 5000
    const starsPositions = new Float32Array(starsCount * 3)
    const starsOpacity = new Float32Array(starsCount)
    
    for (let i = 0; i < starsCount; i++) {
      // 随机分布在3D空间中
      starsPositions[i * 3] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 1] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 2] = (Math.random() - 0.5) * 2000
      
      // 随机初始透明度用于闪烁效果
      starsOpacity[i] = Math.random()
    }

    starsGeometry.setAttribute('position', new THREE.BufferAttribute(starsPositions, 3))
    starsGeometry.setAttribute('opacity', new THREE.BufferAttribute(starsOpacity, 1))

    // 星星材质
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 2,
      sizeAttenuation: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    })

    const stars = new THREE.Points(starsGeometry, starsMaterial)
    scene.add(stars)

    // 创建星云效果
    const nebulae = new THREE.Group()
    
    // 创建3个星云
    const nebulaColors = [
      new THREE.Color('#8B5CF6'), // 紫色
      new THREE.Color('#C084FC'), // 亮紫色  
      new THREE.Color('#F472B6')  // 粉色
    ]

    for (let i = 0; i < 3; i++) {
      const nebulaGeometry = new THREE.PlaneGeometry(400, 400)
      
      // 创建径向渐变纹理
      const canvas = document.createElement('canvas')
      canvas.width = 256
      canvas.height = 256
      const ctx = canvas.getContext('2d')!
      
      const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128)
      const color = nebulaColors[i]
      gradient.addColorStop(0, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.8)`)
      gradient.addColorStop(0.5, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.4)`)
      gradient.addColorStop(1, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0)`)
      
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, 256, 256)
      
      const texture = new THREE.CanvasTexture(canvas)
      
      const nebulaMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide
      })
      
      const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial)
      
      // 随机位置和旋转
      nebula.position.set(
        (Math.random() - 0.5) * 1000,
        (Math.random() - 0.5) * 1000,
        -800 - Math.random() * 200
      )
      nebula.rotation.z = Math.random() * Math.PI * 2
      
      nebulae.add(nebula)
    }
    
    scene.add(nebulae)

    // 存储场景引用
    sceneRef.current = {
      scene,
      camera,
      renderer,
      stars,
      nebulae,
      animationId: null
    }

    // 动画循环
    const animate = () => {
      if (!sceneRef.current) return

      const { scene, camera, renderer, stars, nebulae } = sceneRef.current
      
      // 缓慢旋转星云
      nebulae.rotation.y += 0.001
      
      // 星星闪烁效果
      const opacityArray = stars.geometry.attributes.opacity.array as Float32Array
      for (let i = 0; i < opacityArray.length; i++) {
        opacityArray[i] += (Math.random() - 0.5) * 0.02
        opacityArray[i] = Math.max(0.1, Math.min(1, opacityArray[i]))
      }
      stars.geometry.attributes.opacity.needsUpdate = true

      renderer.render(scene, camera)
      sceneRef.current.animationId = requestAnimationFrame(animate)
    }

    animate()

    // 窗口尺寸变化处理
    const handleResize = () => {
      if (!sceneRef.current) return
      
      const { camera, renderer } = sceneRef.current
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
      
      if (sceneRef.current) {
        if (sceneRef.current.animationId) {
          cancelAnimationFrame(sceneRef.current.animationId)
        }
        
        sceneRef.current.renderer.dispose()
        sceneRef.current.scene.clear()
      }
    }
  }, [isClient])

  // 处理主题变化
  useEffect(() => {
    if (!sceneRef.current) return

    const opacity = darkMode ? 1 : 0.6
    sceneRef.current.stars.material.opacity = opacity
    
    sceneRef.current.nebulae.children.forEach((nebula) => {
      if (nebula instanceof THREE.Mesh) {
        nebula.material.opacity = darkMode ? 1 : 0.4
      }
    })
  }, [darkMode])

  if (!isClient) {
    return null // 服务端渲染时不显示
  }

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: -1 }}
    />
  )
}