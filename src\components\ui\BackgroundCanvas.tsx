'use client'

import { useEffect, useRef, useState } from 'react'
import * as THREE from 'three'
import { useTheme } from '@/contexts/ThemeContext'

interface BackgroundCanvasProps {
  mousePosition?: { x: number; y: number }
}

export default function BackgroundCanvas({ mousePosition }: BackgroundCanvasProps) {
  const { isDark } = useTheme()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sceneRef = useRef<{
    scene: THREE.Scene
    camera: THREE.PerspectiveCamera
    renderer: THREE.WebGLRenderer
    stars: THREE.Points
    nebulae: THREE.Group
    animationId: number | null
    mouse: THREE.Vector2
    rippleEffect: {
      position: THREE.Vector3
      strength: number
      radius: number
    }
  } | null>(null)

  const [isClient, setIsClient] = useState(false)
  const [mouse, setMouse] = useState({ x: 0, y: 0 })
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    setIsClient(true)

    // 鼠标移动事件监听
    const handleMouseMove = (event: MouseEvent) => {
      const x = (event.clientX / window.innerWidth) * 2 - 1
      const y = -(event.clientY / window.innerHeight) * 2 + 1
      setMouse({ x, y })
    }

    // 滚动事件监听
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    if (!isClient || !canvasRef.current) return

    // 初始化Three.js场景
    const scene = new THREE.Scene()
    
    // 设置相机
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      2000
    )
    camera.position.z = 1000

    // 设置渲染器
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: true,
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // 创建星星粒子系统 - 根据设备性能调整数量
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const starsCount = isMobile ? 2000 : 5000

    const starsGeometry = new THREE.BufferGeometry()
    const starsPositions = new Float32Array(starsCount * 3)
    const starsOpacity = new Float32Array(starsCount)
    const starsSizes = new Float32Array(starsCount)

    for (let i = 0; i < starsCount; i++) {
      // 随机分布在3D空间中
      starsPositions[i * 3] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 1] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 2] = (Math.random() - 0.5) * 2000

      // 随机初始透明度用于闪烁效果
      starsOpacity[i] = Math.random()

      // 随机初始大小
      starsSizes[i] = Math.random() * 2 + 1
    }

    starsGeometry.setAttribute('position', new THREE.BufferAttribute(starsPositions, 3))
    starsGeometry.setAttribute('opacity', new THREE.BufferAttribute(starsOpacity, 1))
    starsGeometry.setAttribute('size', new THREE.BufferAttribute(starsSizes, 1))

    // 星星材质 - 支持动态大小
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 2,
      sizeAttenuation: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending,
      vertexColors: false
    })

    const stars = new THREE.Points(starsGeometry, starsMaterial)
    scene.add(stars)

    // 创建星云效果
    const nebulae = new THREE.Group()
    
    // 创建3个星云
    const nebulaColors = [
      new THREE.Color('#8B5CF6'), // 紫色
      new THREE.Color('#C084FC'), // 亮紫色  
      new THREE.Color('#F472B6')  // 粉色
    ]

    for (let i = 0; i < 3; i++) {
      const nebulaGeometry = new THREE.PlaneGeometry(400, 400)
      
      // 创建径向渐变纹理
      const canvas = document.createElement('canvas')
      canvas.width = 256
      canvas.height = 256
      const ctx = canvas.getContext('2d')!
      
      const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128)
      const color = nebulaColors[i]
      gradient.addColorStop(0, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.8)`)
      gradient.addColorStop(0.5, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.4)`)
      gradient.addColorStop(1, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0)`)
      
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, 256, 256)
      
      const texture = new THREE.CanvasTexture(canvas)
      
      const nebulaMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide
      })
      
      const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial)
      
      // 随机位置和旋转
      nebula.position.set(
        (Math.random() - 0.5) * 1000,
        (Math.random() - 0.5) * 1000,
        -800 - Math.random() * 200
      )
      nebula.rotation.z = Math.random() * Math.PI * 2
      
      nebulae.add(nebula)
    }
    
    scene.add(nebulae)

    // 存储场景引用
    sceneRef.current = {
      scene,
      camera,
      renderer,
      stars,
      nebulae,
      animationId: null,
      mouse: new THREE.Vector2(),
      rippleEffect: {
        position: new THREE.Vector3(),
        strength: 0,
        radius: 0
      }
    }

    // 动画循环
    const animate = () => {
      if (!sceneRef.current) return

      const { scene, camera, renderer, stars, nebulae, mouse, rippleEffect } = sceneRef.current

      // 更新鼠标位置
      mouse.x = mouse.x + (mouse.x - mouse.x) * 0.1
      mouse.y = mouse.y + (mouse.y - mouse.y) * 0.1

      // 视差滚动效果
      const scrollOffset = scrollY * 0.0005
      camera.position.y = 1000 + scrollOffset * 200
      camera.position.z = 1000 - scrollOffset * 100

      // 缓慢旋转星云
      nebulae.rotation.y += 0.001
      nebulae.rotation.x = scrollOffset * 0.1

      // 鼠标涟漪效果
      const mouseVector = new THREE.Vector3(mouse.x * 1000, mouse.y * 1000, 0)
      rippleEffect.position.copy(mouseVector)
      rippleEffect.strength = Math.max(0, rippleEffect.strength - 0.02)
      rippleEffect.radius = Math.min(300, rippleEffect.radius + 5)

      // 星星效果更新
      const positionsArray = stars.geometry.attributes.position.array as Float32Array
      const opacityArray = stars.geometry.attributes.opacity.array as Float32Array
      const sizesArray = stars.geometry.attributes.size.array as Float32Array

      for (let i = 0; i < opacityArray.length; i++) {
        // 基础闪烁效果
        opacityArray[i] += (Math.random() - 0.5) * 0.02
        opacityArray[i] = Math.max(0.1, Math.min(1, opacityArray[i]))

        // 鼠标涟漪效果
        const starPos = new THREE.Vector3(
          positionsArray[i * 3],
          positionsArray[i * 3 + 1],
          positionsArray[i * 3 + 2]
        )

        const distance = starPos.distanceTo(rippleEffect.position)
        if (distance < rippleEffect.radius && rippleEffect.strength > 0) {
          const effect = (1 - distance / rippleEffect.radius) * rippleEffect.strength
          sizesArray[i] = Math.max(1, sizesArray[i] + effect * 3)
          opacityArray[i] = Math.min(1, opacityArray[i] + effect * 0.5)
        } else {
          // 恢复原始大小
          sizesArray[i] = Math.max(1, sizesArray[i] * 0.98)
        }
      }

      stars.geometry.attributes.opacity.needsUpdate = true
      stars.geometry.attributes.size.needsUpdate = true

      renderer.render(scene, camera)
      sceneRef.current.animationId = requestAnimationFrame(animate)
    }

    animate()

    // 窗口尺寸变化处理
    const handleResize = () => {
      if (!sceneRef.current) return
      
      const { camera, renderer } = sceneRef.current
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
      
      if (sceneRef.current) {
        if (sceneRef.current.animationId) {
          cancelAnimationFrame(sceneRef.current.animationId)
        }
        
        sceneRef.current.renderer.dispose()
        sceneRef.current.scene.clear()
      }
    }
  }, [isClient])

  // 处理主题变化
  useEffect(() => {
    if (!sceneRef.current) return

    const opacity = darkMode ? 1 : 0.6
    sceneRef.current.stars.material.opacity = opacity
    
    sceneRef.current.nebulae.children.forEach((nebula) => {
      if (nebula instanceof THREE.Mesh) {
        nebula.material.opacity = darkMode ? 1 : 0.4
      }
    })
  }, [darkMode])

  if (!isClient) {
    return null // 服务端渲染时不显示
  }

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: -1 }}
    />
  )
}