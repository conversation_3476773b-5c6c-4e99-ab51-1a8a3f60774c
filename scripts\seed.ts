import { db } from '../src/lib/db'
import { posts, tags, postTags } from '../src/db/schema'

async function seed() {
  console.log('开始插入示例数据...')

  try {
    // 插入标签
    const insertedTags = await db.insert(tags).values([
      { name: '技术', slug: 'tech' },
      { name: 'JavaScript', slug: 'javascript' },
      { name: 'React', slug: 'react' },
      { name: 'Next.js', slug: 'nextjs' },
      { name: '生活', slug: 'life' },
    ]).returning()

    console.log('标签插入成功:', insertedTags)

    // 插入文章
    const insertedPosts = await db.insert(posts).values([
      {
        title: 'Hello World - 我的第一篇博客',
        slug: 'hello-world',
        content: `# 欢迎来到我的博客

这是我的第一篇博客文章，使用 Next.js + MDX 构建。

## 特性

- ✅ 支持 Markdown
- ✅ 代码高亮
- ✅ 响应式设计
- ✅ 深色模式

\`\`\`javascript
console.log("Hello, World!")
\`\`\`

期待与你分享更多内容！`,
        excerpt: '我的第一篇博客文章，介绍了这个博客的基本功能和特性。',
        published: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
      {
        title: 'Next.js 14 App Router 完全指南',
        slug: 'nextjs-14-app-router-guide',
        content: `# Next.js 14 App Router 完全指南

Next.js 14 带来了许多令人兴奋的新特性，其中 App Router 是最重要的更新之一。

## 什么是 App Router？

App Router 是 Next.js 13 引入的新路由系统，基于 React Server Components 构建。

## 主要特性

### 1. 文件系统路由
- \`app/page.tsx\` - 页面组件
- \`app/layout.tsx\` - 布局组件
- \`app/loading.tsx\` - 加载组件
- \`app/error.tsx\` - 错误组件

### 2. Server Components
默认情况下，所有组件都是 Server Components，这意味着：
- 更好的性能
- 更小的 bundle 大小
- 更好的 SEO

\`\`\`tsx
// app/page.tsx
export default function Page() {
  return <h1>Hello, App Router!</h1>
}
\`\`\`

## 总结

App Router 为 Next.js 应用带来了更好的性能和开发体验。`,
        excerpt: '深入了解 Next.js 14 App Router 的核心概念和最佳实践。',
        published: true,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
      },
      {
        title: 'TailwindCSS 实用技巧分享',
        slug: 'tailwindcss-tips-and-tricks',
        content: `# TailwindCSS 实用技巧分享

TailwindCSS 是一个功能强大的原子化 CSS 框架，这里分享一些实用技巧。

## 1. 自定义配置

\`\`\`javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#3B82F6',
        secondary: '#64748B',
      }
    }
  }
}
\`\`\`

## 2. 响应式设计

\`\`\`html
<div class="w-full md:w-1/2 lg:w-1/3">
  响应式容器
</div>
\`\`\`

## 3. 深色模式

\`\`\`html
<div class="bg-white dark:bg-gray-900 text-black dark:text-white">
  支持深色模式的内容
</div>
\`\`\`

这些技巧能帮助你更高效地使用 TailwindCSS。`,
        excerpt: '分享一些 TailwindCSS 的实用技巧和最佳实践。',
        published: true,
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20'),
      }
    ]).returning()

    console.log('文章插入成功:', insertedPosts)

    // 为文章添加标签关联
    await db.insert(postTags).values([
      { postId: insertedPosts[0].id, tagId: insertedTags[0].id }, // Hello World - 技术
      { postId: insertedPosts[1].id, tagId: insertedTags[0].id }, // Next.js 指南 - 技术
      { postId: insertedPosts[1].id, tagId: insertedTags[1].id }, // Next.js 指南 - JavaScript
      { postId: insertedPosts[1].id, tagId: insertedTags[3].id }, // Next.js 指南 - Next.js
      { postId: insertedPosts[2].id, tagId: insertedTags[0].id }, // TailwindCSS - 技术
    ])

    console.log('标签关联插入成功')
    console.log('示例数据插入完成！')

  } catch (error) {
    console.error('插入数据时出错:', error)
  }
}

seed().catch(console.error)