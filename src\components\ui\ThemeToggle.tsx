'use client'

import { useTheme } from '@/contexts/ThemeContext'
import { Sun, Moon } from 'lucide-react'

export default function ThemeToggle() {
  const { theme, toggleTheme, isDark } = useTheme()

  return (
    <button
      onClick={toggleTheme}
      className="relative p-3 rounded-full bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 transition-all duration-300 group"
      aria-label={`切换到${isDark ? '浅色' : '深色'}模式`}
    >
      {/* 太阳图标 */}
      <Sun 
        className={`w-5 h-5 text-yellow-400 transition-all duration-300 ${
          isDark 
            ? 'opacity-0 rotate-90 scale-0' 
            : 'opacity-100 rotate-0 scale-100'
        }`}
        style={{
          position: isDark ? 'absolute' : 'static',
          top: isDark ? '50%' : 'auto',
          left: isDark ? '50%' : 'auto',
          transform: isDark ? 'translate(-50%, -50%)' : 'none'
        }}
      />
      
      {/* 月亮图标 */}
      <Moon 
        className={`w-5 h-5 text-blue-300 transition-all duration-300 ${
          isDark 
            ? 'opacity-100 rotate-0 scale-100' 
            : 'opacity-0 -rotate-90 scale-0'
        }`}
        style={{
          position: !isDark ? 'absolute' : 'static',
          top: !isDark ? '50%' : 'auto',
          left: !isDark ? '50%' : 'auto',
          transform: !isDark ? 'translate(-50%, -50%)' : 'none'
        }}
      />
      
      {/* 发光效果 */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400/20 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
      
      {/* 涟漪效果 */}
      <div className="absolute inset-0 rounded-full border border-white/30 scale-100 group-hover:scale-110 transition-transform duration-300"></div>
    </button>
  )
}
