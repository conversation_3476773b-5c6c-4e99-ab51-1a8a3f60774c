# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese personal blog built with Next.js 15, featuring a full-stack architecture with PostgreSQL database integration. The blog supports MDX content rendering and includes an admin interface for content management.

## Tech Stack

- **Framework**: Next.js 15 with App Router and Turbopack
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: Tailwind CSS v4
- **Content**: MDX support with rehype plugins for syntax highlighting and GitHub Flavored Markdown
- **Languages**: TypeScript, Chinese UI text

## Development Commands

```bash
# Development
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production  
pnpm start        # Start production server
pnpm lint         # Run ESLint

# Database operations
pnpm db:generate  # Generate Drizzle schema migrations
pnpm db:push      # Push schema changes to database
pnpm db:studio    # Launch Drizzle Studio for database management
pnpm db:seed      # Run database seeding script

# Docker development
docker-compose -f docker-compose.dev.yml up  # Start PostgreSQL container
```

## Architecture

### Database Schema
- **posts**: Main blog posts table with title, slug, content, excerpt, published status
- **tags**: Tag system for categorizing posts
- **postTags**: Many-to-many relationship between posts and tags

### Directory Structure
- `src/app/`: Next.js App Router pages and layouts
  - `blog/`: Blog listing and individual post pages  
  - `admin/`: Admin interface for content management
  - `about/`: About page
- `src/components/`: Reusable React components
  - `blog/`: Blog-specific components (markdown renderer, etc.)
  - `admin/`: Admin interface components
- `src/db/`: Database configuration and schema
- `src/lib/`: Utility functions and database operations
- `content/posts/`: MDX blog post files
- `scripts/`: Database seeding and utility scripts

### Key Features
- Chinese language interface with navigation (首页, 博客, 关于)
- MDX content rendering with syntax highlighting
- Database-driven blog posts with slug-based routing
- Tag system for post categorization
- Admin interface for content management
- Docker development environment

### Database Connection
The project uses hardcoded database credentials in `drizzle.config.ts` for local development:
- Host: localhost:5432
- Database: blog_db
- User: postgres
- Password: 123456

Ensure PostgreSQL is running locally or use the provided Docker Compose configuration.