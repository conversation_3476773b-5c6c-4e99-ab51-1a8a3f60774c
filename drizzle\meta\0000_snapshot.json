{"id": "fe2f68ef-ad18-486b-bd8d-3f814764980a", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.post_tags": {"name": "post_tags", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "post_id": {"name": "post_id", "type": "integer", "primaryKey": false, "notNull": false}, "tag_id": {"name": "tag_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"post_tags_post_id_posts_id_fk": {"name": "post_tags_post_id_posts_id_fk", "tableFrom": "post_tags", "tableTo": "posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "post_tags_tag_id_tags_id_fk": {"name": "post_tags_tag_id_tags_id_fk", "tableFrom": "post_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "published": {"name": "published", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"posts_slug_unique": {"name": "posts_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tags_name_unique": {"name": "tags_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "tags_slug_unique": {"name": "tags_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}