import { Metadata } from 'next'
import { getPublishedPosts } from '@/lib/posts'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export const metadata: Metadata = {
  title: '博客文章',
  description: '我的个人博客文章列表',
}

export default async function BlogPage() {
  const posts = await getPublishedPosts()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8">博客文章</h1>
      <div className="grid gap-6">
        {posts.length === 0 ? (
          <p className="text-gray-600">暂无文章，请先添加一些内容。</p>
        ) : (
          posts.map((post) => (
            <article key={post.id} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
              <h2 className="text-2xl font-semibold mb-2">
                <a href={`/blog/${post.slug}`} className="hover:text-blue-600">
                  {post.title}
                </a>
              </h2>
              {post.excerpt && (
                <p className="text-gray-600 mb-4">{post.excerpt}</p>
              )}
              <div className="flex items-center text-sm text-gray-500">
                {post.createdAt && (
                  <time>
                    {format(new Date(post.createdAt), 'yyyy年MM月dd日', { locale: zhCN })}
                  </time>
                )}
                <span className="mx-2">•</span>
                <span>5 分钟阅读</span>
              </div>
            </article>
          ))
        )}
      </div>
    </div>
  )
}