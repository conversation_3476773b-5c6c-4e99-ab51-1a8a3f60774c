'use client'

import { ReactNode } from 'react'
import { ThemeProvider } from '@/contexts/ThemeContext'
import BackgroundCanvas from '@/components/ui/BackgroundCanvas'
import ThemeToggle from '@/components/ui/ThemeToggle'

interface ClientLayoutProps {
  children: ReactNode
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <ThemeProvider>
      <BackgroundCanvas />
      
      <div className="relative z-10">
        <Header />
        
        <main className="min-h-screen relative z-10">
          {children}
        </main>
      </div>
    </ThemeProvider>
  )
}

function Header() {
  return (
    <header className="border-b shadow-sm bg-white/10 dark:bg-black/20 backdrop-blur-md sticky top-0 z-50">
      <nav className="container mx-auto px-6 py-8">
        <div className="flex items-center">
          {/* 左侧 Logo */}
          <div className="flex-shrink-0">
            <a href="/" className="text-3xl font-bold text-white hover:text-purple-300 transition-colors duration-300" style={{fontFamily: 'var(--font-noto-sans-sc)'}}>
              我的博客
            </a>
          </div>
          
          {/* 中间导航区域 */}
          <div className="flex-1 flex justify-center mx-16">
            <div className="flex items-center justify-between w-96">
              <a
                href="/"
                className="relative text-xl font-semibold text-white/90 hover:text-purple-300 transition-all duration-300 py-3 px-6 rounded-xl hover:bg-white/10 group"
                style={{fontFamily: 'var(--font-noto-sans-sc)'}}
              >
                首页
                <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
              </a>
              <a
                href="/blog"
                className="relative text-xl font-semibold text-white/90 hover:text-purple-300 transition-all duration-300 py-3 px-6 rounded-xl hover:bg-white/10 group"
                style={{fontFamily: 'var(--font-noto-sans-sc)'}}
              >
                博客
                <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
              </a>
              <a
                href="/about"
                className="relative text-xl font-semibold text-white/90 hover:text-purple-300 transition-all duration-300 py-3 px-6 rounded-xl hover:bg-white/10 group"
                style={{fontFamily: 'var(--font-noto-sans-sc)'}}
              >
                关于
                <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
              </a>
            </div>
          </div>
          
          {/* 右侧主题切换按钮 */}
          <div className="flex-shrink-0">
            <ThemeToggle />
          </div>
        </div>
      </nav>
    </header>
  )
}
