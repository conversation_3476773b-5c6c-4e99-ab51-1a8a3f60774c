import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getPostBySlug, getPublishedPosts } from '@/lib/posts'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import MarkdownRenderer from '@/components/blog/markdown-renderer'

interface BlogPostProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  const posts = await getPublishedPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export async function generateMetadata({ params }: BlogPostProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getPostBySlug(resolvedParams.slug)

  if (!post) {
    return {
      title: '文章未找到',
      description: '请求的文章不存在',
    }
  }

  return {
    title: `${post.title} - 我的博客`,
    description: post.excerpt || '博客文章详情页',
  }
}

export default async function BlogPost({ params }: BlogPostProps) {
  const resolvedParams = await params
  const post = await getPostBySlug(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <article className="prose prose-lg mx-auto">
        <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
        <div className="text-gray-600 mb-8 border-b pb-4">
          {post.createdAt && (
            <time>
              {format(new Date(post.createdAt), 'yyyy年MM月dd日', { locale: zhCN })}
            </time>
          )}
          <span className="mx-2">•</span>
          <span>5分钟阅读</span>
        </div>

        <MarkdownRenderer content={post.content} />
      </article>
    </div>
  )
}